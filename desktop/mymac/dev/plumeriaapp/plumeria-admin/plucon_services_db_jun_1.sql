-- MySQL dump 10.13  Distrib 9.2.0, for macos15.2 (arm64)
--
-- Host: localhost    Database: plucon_services
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `blood_groups`
--

DROP TABLE IF EXISTS `blood_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `blood_groups` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `blood_groups_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `blood_groups_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blood_groups`
--

LOCK TABLES `blood_groups` WRITE;
/*!40000 ALTER TABLE `blood_groups` DISABLE KEYS */;
INSERT INTO `blood_groups` VALUES (2,'A +ve',1,5,NULL,'2025-05-18 03:53:24','2025-05-18 03:53:24');
/*!40000 ALTER TABLE `blood_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `brands`
--

DROP TABLE IF EXISTS `brands`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brands` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `product_subcategory_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_brands_name_subcategory` (`name`,`product_subcategory_id`),
  KEY `fk_brands_created_by` (`created_by`),
  KEY `fk_brands_updated_by` (`updated_by`),
  KEY `idx_brands_name` (`name`),
  KEY `idx_brands_subcategory` (`product_subcategory_id`),
  KEY `idx_brands_active` (`is_active`),
  CONSTRAINT `fk_brands_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_brands_product_subcategory` FOREIGN KEY (`product_subcategory_id`) REFERENCES `product_subcategories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_brands_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brands`
--

LOCK TABLES `brands` WRITE;
/*!40000 ALTER TABLE `brands` DISABLE KEYS */;
INSERT INTO `brands` VALUES (1,'Chettinad',3,1,5,5,'2025-05-26 17:11:23','2025-05-26 17:37:02'),(2,'Kundan',4,0,5,5,'2025-05-26 17:25:50','2025-05-26 17:26:23'),(3,'Ultratech',3,0,5,5,'2025-05-26 17:32:50','2025-05-26 17:46:57'),(4,'Shankar',3,1,5,NULL,'2025-05-26 17:36:54','2025-05-26 17:36:54');
/*!40000 ALTER TABLE `brands` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cities`
--

DROP TABLE IF EXISTS `cities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `state_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `state_id` (`state_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `cities_ibfk_1` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`),
  CONSTRAINT `cities_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `cities_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cities`
--

LOCK TABLES `cities` WRITE;
/*!40000 ALTER TABLE `cities` DISABLE KEYS */;
INSERT INTO `cities` VALUES (1,'Mumbai','MUM',1,1,5,NULL,'2025-05-17 03:38:18','2025-05-17 03:38:18'),(2,'Cochin','CH',2,1,5,NULL,'2025-05-17 14:37:43','2025-05-17 14:37:43'),(3,'Salem','SLM',1,1,7,NULL,'2025-05-26 10:18:40','2025-05-26 10:18:40');
/*!40000 ALTER TABLE `cities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `countries`
--

DROP TABLE IF EXISTS `countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `countries` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(10) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `code` (`code`),
  KEY `fk_countries_created_by` (`created_by`),
  KEY `fk_countries_updated_by` (`updated_by`),
  CONSTRAINT `fk_countries_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_countries_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `countries`
--

LOCK TABLES `countries` WRITE;
/*!40000 ALTER TABLE `countries` DISABLE KEYS */;
INSERT INTO `countries` VALUES (1,'India','IN',1,'2025-05-16 18:09:06','2025-05-26 10:18:10',1,7);
/*!40000 ALTER TABLE `countries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `departments`
--

DROP TABLE IF EXISTS `departments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `departments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `departments_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `departments_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `departments`
--

LOCK TABLES `departments` WRITE;
/*!40000 ALTER TABLE `departments` DISABLE KEYS */;
INSERT INTO `departments` VALUES (1,'Test Department','Dept',1,5,NULL,'2025-05-17 18:05:32','2025-05-17 18:05:32'),(2,'Quantity Surveying','QS',1,7,NULL,'2025-05-26 10:21:17','2025-05-26 10:21:17'),(3,'Execution','Ex',1,7,NULL,'2025-05-26 10:21:38','2025-05-26 10:21:38'),(6,'tests','TX',1,5,NULL,'2025-06-01 08:14:55','2025-06-01 08:14:55');
/*!40000 ALTER TABLE `departments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `designations`
--

DROP TABLE IF EXISTS `designations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `designations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `designations_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `designations_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `designations`
--

LOCK TABLES `designations` WRITE;
/*!40000 ALTER TABLE `designations` DISABLE KEYS */;
INSERT INTO `designations` VALUES (1,'Site Engineer','SE',1,5,NULL,'2025-05-17 18:06:27','2025-05-17 18:06:27'),(2,'Project Manager','PM',1,7,NULL,'2025-05-26 10:21:52','2025-05-26 10:21:52');
/*!40000 ALTER TABLE `designations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_types`
--

DROP TABLE IF EXISTS `employment_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employment_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `employment_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `employment_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_types`
--

LOCK TABLES `employment_types` WRITE;
/*!40000 ALTER TABLE `employment_types` DISABLE KEYS */;
INSERT INTO `employment_types` VALUES (2,'Permanent',1,5,NULL,'2025-05-23 03:09:47','2025-05-23 03:09:47'),(3,'Temporary',1,7,NULL,'2025-05-26 10:22:16','2025-05-26 10:22:16');
/*!40000 ALTER TABLE `employment_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gst_values`
--

DROP TABLE IF EXISTS `gst_values`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gst_values` (
  `id` int NOT NULL AUTO_INCREMENT,
  `value` decimal(5,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `gst_values_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `gst_values_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gst_values`
--

LOCK TABLES `gst_values` WRITE;
/*!40000 ALTER TABLE `gst_values` DISABLE KEYS */;
INSERT INTO `gst_values` VALUES (1,0.00,1,1,NULL,'2025-05-26 03:07:27','2025-05-26 03:07:27'),(2,5.00,1,1,NULL,'2025-05-26 03:07:27','2025-05-26 03:07:27'),(3,12.00,1,1,NULL,'2025-05-26 03:07:27','2025-05-26 03:07:27'),(4,18.00,1,1,NULL,'2025-05-26 03:07:27','2025-05-26 03:07:27'),(5,28.00,1,1,NULL,'2025-05-26 03:07:27','2025-05-26 03:07:27');
/*!40000 ALTER TABLE `gst_values` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `items`
--

DROP TABLE IF EXISTS `items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `category_id` int NOT NULL,
  `subcategory_id` int NOT NULL,
  `unit_of_measurement` varchar(50) NOT NULL,
  `gst_value_id` int NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `subcategory_id` (`subcategory_id`),
  KEY `gst_value_id` (`gst_value_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `items_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`),
  CONSTRAINT `items_ibfk_2` FOREIGN KEY (`subcategory_id`) REFERENCES `product_subcategories` (`id`),
  CONSTRAINT `items_ibfk_3` FOREIGN KEY (`gst_value_id`) REFERENCES `gst_values` (`id`),
  CONSTRAINT `items_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `items_ibfk_5` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `items`
--

LOCK TABLES `items` WRITE;
/*!40000 ALTER TABLE `items` DISABLE KEYS */;
/*!40000 ALTER TABLE `items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `localities`
--

DROP TABLE IF EXISTS `localities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `localities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `city_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `city_id` (`city_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `localities_ibfk_1` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`),
  CONSTRAINT `localities_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `localities_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `localities`
--

LOCK TABLES `localities` WRITE;
/*!40000 ALTER TABLE `localities` DISABLE KEYS */;
INSERT INTO `localities` VALUES (3,'test localitys',1,1,5,5,'2025-05-18 04:06:32','2025-05-18 16:48:43'),(4,'Gandhi Nagar',3,1,7,NULL,'2025-05-26 10:18:55','2025-05-26 10:18:55');
/*!40000 ALTER TABLE `localities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `measurement_units`
--

DROP TABLE IF EXISTS `measurement_units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `measurement_units` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `symbol` varchar(20) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `fk_measurement_units_updated_by` (`updated_by`),
  KEY `idx_measurement_units_name` (`name`),
  KEY `idx_measurement_units_symbol` (`symbol`),
  KEY `idx_measurement_units_active` (`is_active`),
  KEY `idx_measurement_units_created_by` (`created_by`),
  CONSTRAINT `fk_measurement_units_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_measurement_units_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `measurement_units`
--

LOCK TABLES `measurement_units` WRITE;
/*!40000 ALTER TABLE `measurement_units` DISABLE KEYS */;
INSERT INTO `measurement_units` VALUES (1,'Meter','m',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(2,'Centimeter','cm',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(3,'Millimeter','mm',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(4,'Kilometer','km',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(5,'Inch','in',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(6,'Foot','ft',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(7,'Yard','yd',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(8,'Running Meter','rm',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(9,'Running Foot','rf',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(10,'Square Meter','m²',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(11,'Square Centimeter','cm²',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(12,'Square Foot','sq ft',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(13,'Square Yard','sq yd',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(14,'Acre','acre',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(15,'Hectare','ha',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(16,'Square Feet','sft',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(17,'Cubic Meter','m³',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(18,'Cubic Centimeter','cm³',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(19,'Cubic Foot','cu ft',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(20,'Cubic Yard','cu yd',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(21,'Liter','L',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(22,'Milliliter','mL',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(23,'Gallon','gal',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(24,'Cubic Feet','cft',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(25,'Brass','brass',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(26,'Kilogram','kg',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(27,'Gram','g',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(28,'Ton','t',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(29,'Pound','lb',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(30,'Ounce','oz',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(31,'Quintal','q',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(32,'Piece','pcs',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(33,'Dozen','doz',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(34,'Hundred','100s',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(35,'Thousand','1000s',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(36,'Pair','pr',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(37,'Set','set',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(38,'Bundle','bdl',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(39,'Box','box',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(40,'Bag','bag',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(41,'Carton','ctn',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(42,'Roll','roll',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33'),(43,'Sheet','sheet',1,1,NULL,'2025-05-26 03:43:33','2025-05-26 03:43:33');
/*!40000 ALTER TABLE `measurement_units` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `modules`
--

DROP TABLE IF EXISTS `modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `modules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `fk_modules_created_by` (`created_by`),
  CONSTRAINT `fk_modules_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `modules`
--

LOCK TABLES `modules` WRITE;
/*!40000 ALTER TABLE `modules` DISABLE KEYS */;
INSERT INTO `modules` VALUES (1,'module','Module management system',1,'2025-04-27 12:00:20',1),(2,'user','User management system',1,'2025-04-27 12:07:23',1),(3,'invoice','Invoice management system',1,'2025-04-27 12:07:23',1),(4,'role','Role management module',1,'2025-04-27 15:17:56',1),(5,'permission','Permission Management',1,'2025-05-12 17:25:19',1),(13,'masters','Masters module',1,'2025-05-16 18:38:43',5),(14,'staff','Staff management module for employee records',1,'2025-05-31 15:27:48',1);
/*!40000 ALTER TABLE `modules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'create','Create new resources','2025-04-27 11:43:57'),(2,'read','View existing resources','2025-04-27 11:43:57'),(3,'update','Modify existing resources','2025-04-27 11:43:57'),(4,'delete','Remove existing resources','2025-04-27 11:43:57'),(13,'role_create','create permission for role module','2025-04-27 15:17:56');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_categories`
--

DROP TABLE IF EXISTS `product_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_product_categories_created_by_idx` (`created_by`),
  KEY `fk_product_categories_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_product_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_product_categories_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `product_categories_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `product_categories_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_categories`
--

LOCK TABLES `product_categories` WRITE;
/*!40000 ALTER TABLE `product_categories` DISABLE KEYS */;
INSERT INTO `product_categories` VALUES (1,'Carpentry','',1,5,7,'2025-05-25 19:18:44','2025-05-26 10:26:14'),(2,'Electrical','',1,7,NULL,'2025-05-26 10:26:23','2025-05-26 10:26:23'),(3,'Civil','',1,7,NULL,'2025-05-26 10:26:32','2025-05-26 10:26:32');
/*!40000 ALTER TABLE `product_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_subcategories`
--

DROP TABLE IF EXISTS `product_subcategories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_subcategories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `category_id` int NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_category_unique` (`name`,`category_id`),
  KEY `category_id` (`category_id`),
  KEY `fk_product_subcategories_created_by_idx` (`created_by`),
  KEY `fk_product_subcategories_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_product_subcategories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_product_subcategories_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `product_subcategories_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`),
  CONSTRAINT `product_subcategories_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `product_subcategories_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_subcategories`
--

LOCK TABLES `product_subcategories` WRITE;
/*!40000 ALTER TABLE `product_subcategories` DISABLE KEYS */;
INSERT INTO `product_subcategories` VALUES (1,'Hello',1,'\ntest',1,5,NULL,'2025-05-25 19:29:47','2025-05-25 19:29:47'),(3,'Cement',3,'',1,7,NULL,'2025-05-26 10:26:48','2025-05-26 10:26:48'),(4,'Wire',2,'',1,7,NULL,'2025-05-26 10:27:22','2025-05-26 10:27:22');
/*!40000 ALTER TABLE `product_subcategories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `project_statuses`
--

DROP TABLE IF EXISTS `project_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_statuses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `project_statuses_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `project_statuses_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `project_statuses`
--

LOCK TABLES `project_statuses` WRITE;
/*!40000 ALTER TABLE `project_statuses` DISABLE KEYS */;
INSERT INTO `project_statuses` VALUES (1,'Ongoing',1,5,7,'2025-05-18 13:47:20','2025-05-26 10:41:43');
/*!40000 ALTER TABLE `project_statuses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `project_types`
--

DROP TABLE IF EXISTS `project_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `project_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `project_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `project_types`
--

LOCK TABLES `project_types` WRITE;
/*!40000 ALTER TABLE `project_types` DISABLE KEYS */;
/*!40000 ALTER TABLE `project_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qualifications`
--

DROP TABLE IF EXISTS `qualifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qualifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `qualifications_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `qualifications_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qualifications`
--

LOCK TABLES `qualifications` WRITE;
/*!40000 ALTER TABLE `qualifications` DISABLE KEYS */;
INSERT INTO `qualifications` VALUES (1,'Qualification',1,5,5,'2025-05-18 13:33:47','2025-05-18 13:36:29'),(2,'B.E. Civil',1,7,NULL,'2025-05-26 10:22:33','2025-05-26 10:22:33'),(3,'Diploma in Civil',1,7,NULL,'2025-05-26 10:22:40','2025-05-26 10:22:40');
/*!40000 ALTER TABLE `qualifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_module_permissions`
--

DROP TABLE IF EXISTS `role_module_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_module_permissions` (
  `role_id` int NOT NULL,
  `module_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`role_id`,`module_id`,`permission_id`),
  KEY `module_id` (`module_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_module_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_module_permissions_ibfk_2` FOREIGN KEY (`module_id`) REFERENCES `modules` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_module_permissions_ibfk_3` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_module_permissions`
--

LOCK TABLES `role_module_permissions` WRITE;
/*!40000 ALTER TABLE `role_module_permissions` DISABLE KEYS */;
INSERT INTO `role_module_permissions` VALUES (1,1,1),(1,1,2),(1,1,3),(1,1,4),(1,1,13),(2,1,1),(2,1,2),(2,1,3),(2,1,4),(1,2,1),(1,2,2),(1,2,3),(1,2,4),(2,2,1),(2,2,2),(2,2,3),(2,2,4),(3,2,1),(3,2,2),(3,2,3),(3,2,4),(3,2,13),(1,3,1),(1,3,2),(1,3,3),(1,3,4),(1,3,13),(2,3,1),(2,3,2),(2,3,3),(2,3,4),(1,4,1),(1,4,2),(1,4,3),(1,4,4),(1,4,13),(2,4,1),(2,4,2),(2,4,3),(2,4,4),(2,4,13),(3,4,1),(3,4,2),(3,4,3),(3,4,4),(3,4,13),(1,5,1),(1,5,2),(1,5,3),(1,5,4),(1,13,1),(1,13,2),(1,13,3),(1,13,4),(1,14,1),(1,14,2),(1,14,3),(1,14,4);
/*!40000 ALTER TABLE `role_module_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `fk_roles_created_by` (`created_by`),
  CONSTRAINT `fk_roles_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'admin','main admin character','2025-04-27 11:45:05',1,NULL),(2,'manager','System manager with limited access','2025-04-27 12:08:28',1,NULL),(3,'Project Manager','the project manager who manages all projects','2025-04-27 19:13:50',1,4),(10,'Account','Accounts\n','2025-05-26 10:11:51',1,7);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `staffs`
--

DROP TABLE IF EXISTS `staffs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staffs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_name` varchar(100) NOT NULL,
  `staff_mobile` varchar(15) NOT NULL,
  `staff_email` varchar(100) NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `user_role_id` int DEFAULT NULL,
  `date_of_birth` date NOT NULL,
  `marital_status` enum('Single','Married','Divorced','Widowed') NOT NULL,
  `blood_group_id` int DEFAULT NULL,
  `joining_date` date NOT NULL,
  `emergency_contact_name` varchar(100) NOT NULL,
  `emergency_contact_phone` varchar(15) NOT NULL,
  `emergency_contact_relation` varchar(50) NOT NULL,
  `designation_id` int NOT NULL,
  `employment_type_id` int NOT NULL,
  `health_insurance_provider` varchar(100) DEFAULT NULL,
  `health_insurance_number` varchar(50) DEFAULT NULL,
  `department_id` int NOT NULL,
  `salary_amount` decimal(10,2) DEFAULT NULL,
  `salary_last_hiked_date` date DEFAULT NULL,
  `staff_address` text NOT NULL,
  `locality_id` int NOT NULL,
  `city_id` int NOT NULL,
  `pincode` varchar(10) NOT NULL,
  `state_id` int NOT NULL,
  `aadhaar_number` varchar(12) DEFAULT NULL,
  `pan_number` varchar(10) DEFAULT NULL,
  `qualification_id` int DEFAULT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `account_number` varchar(20) DEFAULT NULL,
  `ifsc_code` varchar(11) DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` timestamp NULL DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `login_attempts` int DEFAULT '0',
  `account_locked_until` timestamp NULL DEFAULT NULL,
  `mobile_verified` tinyint(1) DEFAULT '0',
  `mobile_verification_token` varchar(6) DEFAULT NULL,
  `mobile_verification_expires` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `staff_email` (`staff_email`),
  UNIQUE KEY `uk_staffs_email` (`staff_email`),
  UNIQUE KEY `aadhaar_number` (`aadhaar_number`),
  UNIQUE KEY `pan_number` (`pan_number`),
  UNIQUE KEY `uk_staffs_aadhaar` (`aadhaar_number`),
  UNIQUE KEY `uk_staffs_pan` (`pan_number`),
  KEY `fk_staffs_user_role` (`user_role_id`),
  KEY `fk_staffs_blood_group` (`blood_group_id`),
  KEY `fk_staffs_employment_type` (`employment_type_id`),
  KEY `fk_staffs_locality` (`locality_id`),
  KEY `fk_staffs_city` (`city_id`),
  KEY `fk_staffs_state` (`state_id`),
  KEY `fk_staffs_qualification` (`qualification_id`),
  KEY `fk_staffs_created_by` (`created_by`),
  KEY `fk_staffs_updated_by` (`updated_by`),
  KEY `idx_staffs_name` (`staff_name`),
  KEY `idx_staffs_email` (`staff_email`),
  KEY `idx_staffs_mobile` (`staff_mobile`),
  KEY `idx_staffs_department` (`department_id`),
  KEY `idx_staffs_designation` (`designation_id`),
  KEY `idx_staffs_active` (`is_active`),
  KEY `idx_staffs_joining_date` (`joining_date`),
  KEY `idx_staffs_mobile_password` (`staff_mobile`,`password`),
  KEY `idx_staffs_password_reset_token` (`password_reset_token`),
  KEY `idx_staffs_mobile_verification` (`mobile_verification_token`),
  CONSTRAINT `fk_staffs_blood_group` FOREIGN KEY (`blood_group_id`) REFERENCES `blood_groups` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_staffs_city` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staffs_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staffs_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staffs_designation` FOREIGN KEY (`designation_id`) REFERENCES `designations` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staffs_employment_type` FOREIGN KEY (`employment_type_id`) REFERENCES `employment_types` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staffs_locality` FOREIGN KEY (`locality_id`) REFERENCES `localities` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staffs_qualification` FOREIGN KEY (`qualification_id`) REFERENCES `qualifications` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_staffs_state` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staffs_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_staffs_user_role` FOREIGN KEY (`user_role_id`) REFERENCES `roles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Master table for staff/employee information in construction company';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `staffs`
--

LOCK TABLES `staffs` WRITE;
/*!40000 ALTER TABLE `staffs` DISABLE KEYS */;
INSERT INTO `staffs` VALUES (9,'Neha Jain','**********','<EMAIL>','Female',3,'1994-04-14','Married',2,'2025-02-28','Neha Joshi','**********','Friend',2,2,'New India Assurance','POL9595356825',1,424645.00,NULL,'654 Infantry Road, Close to Park',3,3,'135931',4,'************','**********',3,'State Bank of India','****************','UBIN0390494',NULL,NULL,NULL,NULL,NULL,0,NULL,0,NULL,NULL,1,5,NULL,'2025-06-01 09:32:24','2025-06-01 09:32:24'),(10,'Manoj Chopra','**********','<EMAIL>','Male',2,'1998-11-24','Widowed',2,'2024-06-16','Priya Iyer','**********','Spouse',2,3,'Bajaj Allianz','POL4748485073',1,305321.00,'2025-05-07','456 Brigade Road, Opposite Metro Station',4,3,'434022',2,'************','**********',1,'Punjab National Bank','****************','SBIN0370809',NULL,NULL,NULL,NULL,NULL,0,NULL,0,NULL,NULL,1,5,NULL,'2025-06-01 09:34:44','2025-06-01 09:34:44'),(11,'Meera Jain','**********','<EMAIL>','Male',1,'1979-01-10','Single',2,'2020-02-02','Sunita Nair','**********','Father',1,2,'Star Health Insurance','POL7310902698',6,567414.00,'2023-12-22','456 Brigade Road, Opposite Metro Station',3,3,'362462',3,'************','**********',2,'Axis Bank','****************','CNRB0139397',NULL,NULL,NULL,NULL,NULL,0,NULL,0,NULL,NULL,1,5,NULL,'2025-06-01 09:37:17','2025-06-01 09:37:17');
/*!40000 ALTER TABLE `staffs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `states`
--

DROP TABLE IF EXISTS `states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `states` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(10) NOT NULL,
  `country_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_name_per_country` (`name`,`country_id`),
  UNIQUE KEY `unique_code_per_country` (`code`,`country_id`),
  KEY `fk_states_created_by` (`created_by`),
  KEY `fk_states_updated_by` (`updated_by`),
  KEY `fk_states_country_id` (`country_id`),
  CONSTRAINT `fk_states_country_id` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`),
  CONSTRAINT `fk_states_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_states_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `states`
--

LOCK TABLES `states` WRITE;
/*!40000 ALTER TABLE `states` DISABLE KEYS */;
INSERT INTO `states` VALUES (1,'Tamil Nadu','TN',1,1,'2025-05-16 18:09:06','2025-05-17 14:38:58',1,5),(2,'Kerala','KL',1,1,'2025-05-16 18:09:06','2025-05-17 19:05:26',1,5),(3,'Karnataka','KA',1,1,'2025-05-16 18:09:06','2025-05-16 18:09:06',1,NULL),(4,'Andhra Pradesh','AP',1,1,'2025-05-16 18:09:06','2025-05-16 18:09:06',1,NULL);
/*!40000 ALTER TABLE `states` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `supplier_types`
--

DROP TABLE IF EXISTS `supplier_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supplier_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_supplier_types_created_by_idx` (`created_by`),
  KEY `fk_supplier_types_updated_by_idx` (`updated_by`),
  CONSTRAINT `fk_supplier_types_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_supplier_types_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `supplier_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `supplier_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `supplier_types`
--

LOCK TABLES `supplier_types` WRITE;
/*!40000 ALTER TABLE `supplier_types` DISABLE KEYS */;
INSERT INTO `supplier_types` VALUES (8,'Wholesale','',1,7,NULL,'2025-05-26 10:25:48','2025-05-26 10:25:48'),(9,'Retail','',1,7,NULL,'2025-05-26 10:26:01','2025-05-26 10:26:01');
/*!40000 ALTER TABLE `supplier_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_module_access`
--

DROP TABLE IF EXISTS `user_module_access`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_module_access` (
  `user_id` int NOT NULL,
  `module_id` int NOT NULL,
  `can_access` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`user_id`,`module_id`),
  KEY `module_id` (`module_id`),
  CONSTRAINT `user_module_access_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_module_access_ibfk_2` FOREIGN KEY (`module_id`) REFERENCES `modules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_module_access`
--

LOCK TABLES `user_module_access` WRITE;
/*!40000 ALTER TABLE `user_module_access` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_module_access` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_roles` (
  `user_id` int NOT NULL,
  `role_id` int NOT NULL,
  PRIMARY KEY (`user_id`,`role_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1),(4,1),(5,1),(7,1),(2,2),(8,3);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'csgokul','<EMAIL>','$2b$10$XiOkZG4UuETymcoBcfMzae9/.yISD7VXUTDEI2/CV.FhiwnjRK8UC','gokul prasad','+1234567890',1,'2025-05-12 17:21:12','2025-05-14 18:18:58'),(2,'sandy','<EMAIL>','$2b$10$kWDHVcv5NCdSS/Q/PKM.SuBefDaTQxkr1UBB7G8pyPRPGXYQJv9Ii','Sandeep karthick','9566712845',1,'2025-05-12 19:32:29','2025-05-16 17:52:03'),(5,'csgokulp','<EMAIL>','$2b$10$jnyWy.fN246tSNC8D1MAiOvgSRgEADMUAmHBGatVwpd19XhtmSrnq','gokul prasad','+91 98437 57161',1,'2025-05-14 16:42:31','2025-05-15 03:56:10'),(7,'anand','<EMAIL>','$2b$10$76DTWmSFaERmnqIBfQLloup/4tteLbPVj87mu8KwT6zmtiJrFgP8y','Anand','9597422856',1,'2025-05-26 10:10:28','2025-05-26 10:10:28'),(8,'vishnu','<EMAIL>','$2b$10$1Y8ajnWVXyz9YpOhotlbqegFGfwYVy9JDrgC04QGL.PRhvb.yDmAW','Vishnu','9898989898',1,'2025-05-26 10:11:05','2025-05-26 10:11:05');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-01 15:13:52
