import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  Staff,
  StaffCreateRequest,
  StaffUpdateRequest,
  StaffListResponse,
  StaffResponse,
  StaffFilters,
  StaffLoginRequest,
  StaffLoginResponse,
  StaffOTPRequest,
  StaffOTPVerifyRequest,
  StaffPasswordSetRequest,
  StaffPasswordResetRequest,
  StaffPasswordResetVerifyRequest,
} from '../models/staff';

@Injectable({
  providedIn: 'root',
})
export class StaffService {
  private apiUrl = `${environment.apiUrl}/staff`;

  constructor(private http: HttpClient) {}

  /**
   * Get all staff with optional filtering and pagination
   */
  getStaff(filters: StaffFilters = {}): Observable<StaffListResponse> {
    let params = new HttpParams();

    if (filters.search) {
      params = params.set('search', filters.search);
    }
    if (filters.departmentId) {
      params = params.set('departmentId', filters.departmentId.toString());
    }
    if (filters.designationId) {
      params = params.set('designationId', filters.designationId.toString());
    }
    if (filters.includeInactive !== undefined) {
      params = params.set(
        'includeInactive',
        filters.includeInactive.toString()
      );
    }
    if (filters.page) {
      params = params.set('page', filters.page.toString());
    }
    if (filters.limit) {
      params = params.set('limit', filters.limit.toString());
    }

    return this.http.get<StaffListResponse>(this.apiUrl, { params });
  }

  /**
   * Get staff by ID
   */
  getStaffById(id: number): Observable<StaffResponse> {
    return this.http.get<StaffResponse>(`${this.apiUrl}/${id}`);
  }

  /**
   * Create new staff member
   */
  createStaff(staff: StaffCreateRequest): Observable<StaffResponse> {
    return this.http.post<StaffResponse>(this.apiUrl, staff);
  }

  /**
   * Update staff member
   */
  updateStaff(
    id: number,
    staff: StaffUpdateRequest
  ): Observable<StaffResponse> {
    return this.http.put<StaffResponse>(`${this.apiUrl}/${id}`, staff);
  }

  /**
   * Delete staff member
   */
  deleteStaff(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  /**
   * Toggle staff active status
   */
  toggleStaffStatus(id: number, isActive: boolean): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/status`, {
      is_active: isActive,
    });
  }

  /**
   * Bulk delete staff members
   */
  bulkDeleteStaff(ids: number[]): Observable<any> {
    return this.http.post(`${this.apiUrl}/bulk-delete`, { ids });
  }

  /**
   * Bulk update staff status
   */
  bulkUpdateStatus(ids: number[], isActive: boolean): Observable<any> {
    return this.http.post(`${this.apiUrl}/bulk-status`, {
      ids,
      is_active: isActive,
    });
  }

  /**
   * Get staff by IDs
   */
  getStaffByIds(ids: number[]): Observable<StaffListResponse> {
    return this.http.post<StaffListResponse>(`${this.apiUrl}/by-ids`, { ids });
  }

  /**
   * Search staff
   */
  searchStaff(query: string): Observable<StaffListResponse> {
    let params = new HttpParams();
    if (query) {
      params = params.set('q', query);
    }
    return this.http.get<StaffListResponse>(`${this.apiUrl}/search`, {
      params,
    });
  }

  /**
   * Get staff statistics
   */
  getStaffStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/stats`);
  }

  /**
   * Export staff data
   */
  exportStaff(format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    let params = new HttpParams();
    params = params.set('format', format);

    return this.http.get(`${this.apiUrl}/export`, {
      params,
      responseType: 'blob',
    });
  }

  /**
   * Get staff by department
   */
  getStaffByDepartment(
    departmentId: number,
    includeInactive = false
  ): Observable<StaffListResponse> {
    let params = new HttpParams();
    if (includeInactive) {
      params = params.set('includeInactive', 'true');
    }

    return this.http.get<StaffListResponse>(
      `${this.apiUrl}/department/${departmentId}`,
      { params }
    );
  }

  /**
   * Upload profile picture
   */
  uploadProfilePicture(id: number, file: File): Observable<any> {
    console.log('=== SERVICE UPLOAD ===');
    console.log('API URL:', this.apiUrl);
    console.log('Upload URL:', `${this.apiUrl}/${id}/upload-photo`);
    console.log('File:', file.name, file.size, file.type);

    const formData = new FormData();
    formData.append('profile_picture', file);

    // Log FormData contents
    console.log('FormData entries:');
    for (let pair of formData.entries()) {
      console.log(pair[0] + ': ' + pair[1]);
    }

    return this.http.post(`${this.apiUrl}/${id}/upload-photo`, formData);
  }

  /**
   * Remove profile picture
   */
  removeProfilePicture(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}/photo`);
  }

  /**
   * Get profile picture URL
   */
  getProfilePictureUrl(profilePicture: string): string {
    if (!profilePicture) {
      return ''; // Return empty string to show icon instead
    }

    // Extract base URL from environment
    const baseUrl = environment.apiUrl.replace('/api', '');

    // Remove leading slash from profilePicture if it exists to avoid double slashes
    const cleanPath = profilePicture.startsWith('/')
      ? profilePicture.substring(1)
      : profilePicture;

    const fullUrl = `${baseUrl}/${cleanPath}`;
    console.log('Profile picture URL generation:', {
      profilePicture,
      baseUrl,
      cleanPath,
      fullUrl,
    });

    return fullUrl;
  }

  // Staff Authentication Methods (for mobile app management)

  /**
   * Staff login (for mobile app)
   */
  staffLogin(loginData: StaffLoginRequest): Observable<StaffLoginResponse> {
    return this.http.post<StaffLoginResponse>(
      `${this.apiUrl}/auth/login`,
      loginData
    );
  }

  /**
   * Send OTP to staff mobile
   */
  sendOTP(otpData: StaffOTPRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/send-otp`, otpData);
  }

  /**
   * Verify OTP and login
   */
  verifyOTP(verifyData: StaffOTPVerifyRequest): Observable<StaffLoginResponse> {
    return this.http.post<StaffLoginResponse>(
      `${this.apiUrl}/auth/verify-otp`,
      verifyData
    );
  }

  /**
   * Set password for staff (first time setup)
   */
  setPassword(passwordData: StaffPasswordSetRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/set-password`, passwordData);
  }

  /**
   * Request password reset
   */
  requestPasswordReset(resetData: StaffPasswordResetRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/reset-password`, resetData);
  }

  /**
   * Verify password reset and set new password
   */
  verifyPasswordReset(
    verifyData: StaffPasswordResetVerifyRequest
  ): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/verify-reset`, verifyData);
  }

  /**
   * Refresh JWT token
   */
  refreshToken(refreshToken: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/auth/refresh-token`, {
      refreshToken,
    });
  }
}
