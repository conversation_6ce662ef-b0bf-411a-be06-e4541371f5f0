<div class="staff-list-container">
  <div class="list-header">
    <h1>Staff Management</h1>
    <button mat-raised-button color="primary" routerLink="new">
      <mat-icon>add</mat-icon> Add Staff
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Search and filter fields -->
      <div class="filter-container">
        <div class="search-field">
          <mat-form-field appearance="outline">
            <mat-label>Search Staff</mat-label>
            <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()"
                   placeholder="Search by name, email, mobile, department, designation">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>

        <div class="filter-field">
          <mat-form-field appearance="outline">
            <mat-label>Department</mat-label>
            <mat-select [(ngModel)]="selectedDepartmentId" (selectionChange)="applyFilter()">
              <mat-option [value]="null">All Departments</mat-option>
              <mat-option *ngFor="let dept of departments" [value]="dept.id">
                {{ dept.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-field">
          <mat-form-field appearance="outline">
            <mat-label>Designation</mat-label>
            <mat-select [(ngModel)]="selectedDesignationId" (selectionChange)="applyFilter()">
              <mat-option [value]="null">All Designations</mat-option>
              <mat-option *ngFor="let designation of designations" [value]="designation.id">
                {{ designation.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-actions">
          <button mat-stroked-button (click)="clearFilters()" matTooltip="Clear all filters">
            <mat-icon>clear</mat-icon> Clear
          </button>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="action-buttons">
        <div class="left-actions">
          <button mat-raised-button color="warn" (click)="bulkDeleteSelected()"
                  [disabled]="selection.selected.length === 0"
                  matTooltip="Delete selected staff members">
            <mat-icon>delete</mat-icon> Delete Selected ({{ selection.selected.length }})
          </button>

          <button mat-stroked-button color="primary" (click)="bulkActivateSelected()"
                  [disabled]="selection.selected.length === 0"
                  matTooltip="Activate selected staff members">
            <mat-icon>toggle_on</mat-icon> Activate Selected
          </button>

          <button mat-stroked-button color="accent" (click)="bulkDeactivateSelected()"
                  [disabled]="selection.selected.length === 0"
                  matTooltip="Deactivate selected staff members">
            <mat-icon>toggle_off</mat-icon> Deactivate Selected
          </button>

          <mat-slide-toggle
            [(ngModel)]="includeInactive"
            (change)="toggleIncludeInactive()"
            matTooltip="Include inactive staff members"
            class="show-inactive-toggle">
            Show Inactive
          </mat-slide-toggle>

          <button mat-icon-button (click)="refreshList()" matTooltip="Refresh list">
            <mat-icon>refresh</mat-icon>
          </button>
        </div>

        <div class="right-actions">
          <span class="total-count" *ngIf="!isLoading">
            Total: {{ totalStaff }} staff member(s)
          </span>
        </div>
      </div>

      <!-- Loading spinner -->
      <div class="loading-container" *ngIf="isLoading">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading staff members...</p>
      </div>

      <!-- Error message -->
      <div class="error-container" *ngIf="errorMessage && !isLoading">
        <mat-icon color="warn">error</mat-icon>
        <p>{{ errorMessage }}</p>
        <button mat-stroked-button (click)="refreshList()">
          <mat-icon>refresh</mat-icon> Retry
        </button>
      </div>

      <!-- No data message -->
      <div class="no-data-container" *ngIf="!isLoading && !errorMessage && displayedStaff.length === 0">
        <mat-icon>people_outline</mat-icon>
        <p>No staff members found</p>
        <button mat-raised-button color="primary" routerLink="new">
          <mat-icon>add</mat-icon> Add First Staff Member
        </button>
      </div>

      <!-- Data table -->
      <div class="table-container mat-elevation-z2" *ngIf="!isLoading && displayedStaff.length > 0">
        <table mat-table [dataSource]="displayedStaff" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? toggleAllRows() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let staff">
              <mat-checkbox
                (click)="$event.stopPropagation()"
                (change)="$event ? selection.toggle(staff) : null"
                [checked]="selection.isSelected(staff)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- Profile Picture Column - Enhanced with better error handling -->
          <ng-container matColumnDef="profile_picture">
            <th mat-header-cell *matHeaderCellDef> Photo </th>
            <td mat-cell *matCellDef="let staff">
              <div class="profile-picture"
                   [class.show-icon]="!hasProfilePicture(staff)"
                   [title]="'Profile picture for ' + staff.staff_name">
                <!-- Image with enhanced error handling -->
                <img *ngIf="hasProfilePicture(staff) && getProfilePictureUrl(staff)"
                     [src]="getProfilePictureUrl(staff)"
                     [alt]="staff.staff_name + ' profile picture'"
                     (error)="onImageError($event)"

                     loading="lazy"
                     referrerpolicy="no-referrer"
                     crossorigin="anonymous"
                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%; display: block;">

                <!-- Fallback icon -->
                <mat-icon *ngIf="!hasProfilePicture(staff) || !getProfilePictureUrl(staff)"
                          class="profile-icon"
                          [title]="'No profile picture for ' + staff.staff_name">
                  person
                </mat-icon>
              </div>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="staff_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
            <td mat-cell *matCellDef="let staff">
              <div class="staff-name-cell">
                <span class="name">{{ staff.staff_name }}</span>
                <span class="gender" *ngIf="staff.gender">{{ staff.gender }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="staff_email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Email </th>
            <td mat-cell *matCellDef="let staff">
              <a [href]="'mailto:' + staff.staff_email"
                 class="email-link"
                 [title]="'Send email to ' + staff.staff_email">
                {{ staff.staff_email }}
              </a>
            </td>
          </ng-container>

          <!-- Mobile Column -->
          <ng-container matColumnDef="staff_mobile">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Mobile </th>
            <td mat-cell *matCellDef="let staff">
              <a [href]="'tel:' + staff.staff_mobile"
                 class="phone-link"
                 [title]="'Call ' + staff.staff_mobile">
                {{ staff.staff_mobile }}
              </a>
            </td>
          </ng-container>

          <!-- Department Column -->
          <ng-container matColumnDef="department_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Department </th>
            <td mat-cell *matCellDef="let staff">
              <span class="department-badge">{{ staff.department_name || 'Not Assigned' }}</span>
            </td>
          </ng-container>

          <!-- Designation Column -->
          <ng-container matColumnDef="designation_name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Designation </th>
            <td mat-cell *matCellDef="let staff">
              <span class="designation-badge">{{ staff.designation_name || 'Not Assigned' }}</span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef> Status </th>
            <td mat-cell *matCellDef="let staff">
              <span class="status-badge" [class.active]="staff.is_active" [class.inactive]="!staff.is_active">
                {{ staff.is_active ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let staff">
              <button mat-icon-button (click)="toggleStatus(staff)"
                      [matTooltip]="staff.is_active ? 'Deactivate' : 'Activate'">
                <mat-icon>{{ staff.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
              </button>
              <button mat-icon-button [routerLink]="[staff.id]" matTooltip="View details">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button [routerLink]="['edit', staff.id]" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button (click)="deleteStaff(staff)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns; trackBy: trackByStaffId"></tr>
        </table>

        <!-- Paginator -->
        <mat-paginator
          [length]="totalStaff"
          [pageSize]="pageSize"
          [pageSizeOptions]="pageSizeOptions"
          [pageIndex]="pageIndex"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>