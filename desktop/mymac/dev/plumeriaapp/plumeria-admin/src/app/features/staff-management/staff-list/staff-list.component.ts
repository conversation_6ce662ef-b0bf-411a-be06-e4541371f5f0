import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { SelectionModel } from '@angular/cdk/collections';

import { StaffService } from '../../../core/services/staff.service';
import { DepartmentService } from '../../../core/services/masters/department.service';
import { DesignationService } from '../../../core/services/masters/designation.service';
import { Staff, StaffFilters } from '../../../core/models/staff';
import { Department } from '../../../core/models/masters/department';
import { Designation } from '../../../core/models/masters/designation';
import { ConfirmDialogComponent } from '../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-staff-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
    MatSelectModule,
  ],
  templateUrl: './staff-list.component.html',
  styleUrls: ['./staff-list.component.scss'],
})
export class StaffListComponent implements OnInit {
  staff: Staff[] = [];
  displayedStaff: Staff[] = [];
  displayedColumns: string[] = [
    'select',
    'profile_picture',
    'staff_name',
    'staff_email',
    'staff_mobile',
    'department_name',
    'designation_name',
    'status',
    'actions',
  ];
  selection = new SelectionModel<Staff>(true, []);
  isLoading = true;
  errorMessage = '';

  // Filters
  searchTerm = '';
  selectedDepartmentId: number | null = null;
  selectedDesignationId: number | null = null;
  includeInactive = false;

  // Master data for filters
  departments: Department[] = [];
  designations: Designation[] = [];
  loadingDepartments = false;
  loadingDesignations = false;

  // Pagination - FIXED: Remove filteredStaff dependency
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalStaff = 0;

  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private staffService: StaffService,
    private departmentService: DepartmentService,
    private designationService: DesignationService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadDepartments();
    this.loadDesignations();
    this.loadStaff();
  }

  loadDepartments(): void {
    this.loadingDepartments = true;
    this.departmentService.getDepartments(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingDepartments = false;
      },
      error: (error) => {
        console.error('Error loading departments:', error);
        this.loadingDepartments = false;
      },
    });
  }

  loadDesignations(): void {
    this.loadingDesignations = true;
    this.designationService.getDesignations(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.designations = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingDesignations = false;
      },
      error: (error) => {
        console.error('Error loading designations:', error);
        this.loadingDesignations = false;
      },
    });
  }

  loadStaff(): void {
    this.isLoading = true;
    this.errorMessage = '';

    const filters: StaffFilters = {
      page: this.pageIndex + 1,
      limit: this.pageSize,
      includeInactive: this.includeInactive,
    };

    // Only add filters if they have values
    if (this.searchTerm && this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.selectedDepartmentId) {
      filters.departmentId = this.selectedDepartmentId;
    }

    if (this.selectedDesignationId) {
      filters.designationId = this.selectedDesignationId;
    }

    console.log('=== LOADING STAFF ===');
    console.log('Filters being sent:', filters);

    this.staffService.getStaff(filters).subscribe({
      next: (response) => {
        console.log('=== STAFF RESPONSE ===');
        console.log('Raw response:', response);

        if (response.success) {
          this.staff = Array.isArray(response.data) ? response.data : [];
          this.displayedStaff = [...this.staff]; // FIXED: Use staff directly
          this.totalStaff =
            response.pagination?.totalCount || this.staff.length;

          console.log('Staff loaded:', this.staff.length);
          console.log('Total staff count:', this.totalStaff);
          console.log('Pagination:', response.pagination);
        } else {
          this.errorMessage = response.message || 'Failed to load staff';
          this.staff = [];
          this.displayedStaff = [];
          this.totalStaff = 0;
        }
        this.isLoading = false;
        this.selection.clear();
      },
      error: (error) => {
        console.error('=== STAFF ERROR ===');
        console.error('Error:', error);
        this.errorMessage =
          'Error loading staff: ' + this.getErrorMessage(error);
        this.isLoading = false;
        this.staff = [];
        this.displayedStaff = [];
        this.totalStaff = 0;
      },
    });
  }

  applyFilter(): void {
    console.log('=== APPLYING FILTER ===');
    console.log('Search term:', this.searchTerm);
    console.log('Department ID:', this.selectedDepartmentId);
    console.log('Designation ID:', this.selectedDesignationId);
    console.log('Include inactive:', this.includeInactive);

    this.pageIndex = 0;
    if (this.paginator) {
      this.paginator.firstPage();
    }
    this.loadStaff();
  }

  onPageChange(event: any): void {
    console.log('=== PAGE CHANGE ===');
    console.log('Page event:', event);
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadStaff();
  }

  clearFilters(): void {
    console.log('=== CLEARING FILTERS ===');
    this.searchTerm = '';
    this.selectedDepartmentId = null;
    this.selectedDesignationId = null;
    this.includeInactive = false;
    this.pageIndex = 0;
    if (this.paginator) {
      this.paginator.firstPage();
    }
    this.loadStaff();
  }

  refreshList(): void {
    console.log('=== REFRESHING LIST ===');
    this.loadStaff();
  }

  toggleIncludeInactive(): void {
    console.log('=== TOGGLING INACTIVE ===');
    console.log('Include inactive:', this.includeInactive);
    this.pageIndex = 0;
    if (this.paginator) {
      this.paginator.firstPage();
    }
    this.loadStaff();
  }

  // FIXED: Selection methods work with displayedStaff
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedStaff.length;
    return numSelected === numRows && numRows > 0;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.displayedStaff.forEach((row) => this.selection.select(row));
    }
  }

  // Profile picture methods
  getProfilePictureUrl(staff: Staff): string | null {
    if (!staff.profile_picture) {
      return null;
    }
    return this.staffService.getProfilePictureUrl(staff.profile_picture);
  }

  hasProfilePicture(staff: Staff): boolean {
    return !!(staff.profile_picture && staff.profile_picture.trim());
  }

  // Status toggle
  toggleStatus(staff: Staff): void {
    const newStatus = !staff.is_active;
    this.staffService.toggleStaffStatus(staff.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          staff.is_active = newStatus;
          this.snackBar.open(
            `Staff ${newStatus ? 'activated' : 'deactivated'} successfully`,
            'Close',
            { duration: 3000 }
          );
        } else {
          this.snackBar.open(
            response.message || 'Failed to update status',
            'Close',
            { duration: 3000 }
          );
        }
      },
      error: (error) => {
        this.snackBar.open(
          'Error updating status: ' + this.getErrorMessage(error),
          'Close',
          { duration: 3000 }
        );
      },
    });
  }

  // Delete staff
  deleteStaff(staff: Staff): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Staff Member',
        message: `Are you sure you want to delete "${staff.staff_name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.staffService.deleteStaff(staff.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open('Staff member deleted successfully', 'Close', {
                duration: 3000,
              });
              this.loadStaff(); // Reload the list
            } else {
              this.snackBar.open(
                response.message || 'Failed to delete staff member',
                'Close',
                { duration: 3000 }
              );
            }
          },
          error: (error) => {
            this.snackBar.open(
              'Error deleting staff member: ' + this.getErrorMessage(error),
              'Close',
              { duration: 3000 }
            );
          },
        });
      }
    });
  }

  // Bulk delete
  bulkDeleteSelected(): void {
    if (this.selection.selected.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Selected Staff Members',
        message: `Are you sure you want to delete ${this.selection.selected.length} staff member(s)? This action cannot be undone.`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = this.selection.selected.map((staff) => staff.id);
        this.staffService.bulkDeleteStaff(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open(
                `${ids.length} staff member(s) deleted successfully`,
                'Close',
                { duration: 3000 }
              );
              this.selection.clear();
              this.loadStaff();
            } else {
              this.snackBar.open(
                response.message || 'Failed to delete staff members',
                'Close',
                { duration: 3000 }
              );
            }
          },
          error: (error) => {
            this.snackBar.open(
              'Error deleting staff members: ' + this.getErrorMessage(error),
              'Close',
              { duration: 3000 }
            );
          },
        });
      }
    });
  }

  // Bulk activate selected staff
  bulkActivateSelected(): void {
    if (this.selection.selected.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Activate Selected Staff Members',
        message: `Are you sure you want to activate ${this.selection.selected.length} staff member(s)?`,
        confirmText: 'Activate All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = this.selection.selected.map((staff) => staff.id);
        this.staffService.bulkUpdateStatus(ids, true).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open(
                `${ids.length} staff member(s) activated successfully`,
                'Close',
                { duration: 3000 }
              );
              this.selection.clear();
              this.loadStaff();
            } else {
              this.snackBar.open(
                response.message || 'Failed to activate staff members',
                'Close',
                { duration: 3000 }
              );
            }
          },
          error: (error) => {
            this.snackBar.open(
              'Error activating staff members: ' + this.getErrorMessage(error),
              'Close',
              { duration: 3000 }
            );
          },
        });
      }
    });
  }

  // Bulk deactivate selected staff
  bulkDeactivateSelected(): void {
    if (this.selection.selected.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Deactivate Selected Staff Members',
        message: `Are you sure you want to deactivate ${this.selection.selected.length} staff member(s)?`,
        confirmText: 'Deactivate All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = this.selection.selected.map((staff) => staff.id);
        this.staffService.bulkUpdateStatus(ids, false).subscribe({
          next: (response) => {
            if (response.success) {
              this.snackBar.open(
                `${ids.length} staff member(s) deactivated successfully`,
                'Close',
                { duration: 3000 }
              );
              this.selection.clear();
              this.loadStaff();
            } else {
              this.snackBar.open(
                response.message || 'Failed to deactivate staff members',
                'Close',
                { duration: 3000 }
              );
            }
          },
          error: (error) => {
            this.snackBar.open(
              'Error deactivating staff members: ' +
                this.getErrorMessage(error),
              'Close',
              { duration: 3000 }
            );
          },
        });
      }
    });
  }

  private getErrorMessage(error: any): string {
    if (error.error?.message) {
      return error.error.message;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    img.style.display = 'none';

    const container = img.closest('.profile-picture');
    if (container) {
      container.classList.add('show-icon');
    }
  }

  trackByStaffId(_index: number, staff: Staff): number {
    return staff.id;
  }

  // FIXED: Remove dependencies on filteredStaff
  get filteredStaff(): Staff[] {
    return this.displayedStaff;
  }
}
