<div class="staff-detail-container">
  <!-- Header -->
  <div class="detail-header">
    <div class="header-left">
      <button mat-icon-button (click)="onBackToList()" matTooltip="Back to Staff List">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <h1>Staff Details</h1>
    </div>

    <div class="header-actions" *ngIf="staff && !isLoading">
      <button mat-stroked-button (click)="onToggleStatus()"
              [color]="staff.is_active ? 'warn' : 'primary'"
              [matTooltip]="staff.is_active ? 'Deactivate Staff' : 'Activate Staff'">
        <mat-icon>{{ staff.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
        {{ staff.is_active ? 'Deactivate' : 'Activate' }}
      </button>

      <button mat-stroked-button (click)="onEdit()" matTooltip="Edit Staff Details">
        <mat-icon>edit</mat-icon> Edit
      </button>

      <button mat-stroked-button color="warn" (click)="onDelete()" matTooltip="Delete Staff">
        <mat-icon>delete</mat-icon> Delete
      </button>
    </div>
  </div>

  <!-- Loading spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading staff details...</p>
  </div>

  <!-- Error message -->
  <div class="error-container" *ngIf="errorMessage && !isLoading">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ errorMessage }}</p>
    <button mat-stroked-button (click)="loadStaffData()">
      <mat-icon>refresh</mat-icon> Retry
    </button>
  </div>

  <!-- Staff details content -->
  <div class="detail-content" *ngIf="staff && !isLoading">
    <!-- Profile Overview Card -->
    <mat-card class="profile-overview">
      <mat-card-content>
        <div class="profile-header">
          <!-- Enhanced Profile Picture with Better Error Handling -->
          <div class="profile-picture" 
               [class.show-fallback]="!hasProfilePicture()"
               [title]="'Profile picture for ' + staff.staff_name">
            
            <!-- Profile Image -->
            <img *ngIf="hasProfilePicture() && getProfilePictureUrl()"
                 [src]="getProfilePictureUrl()"
                 [alt]="staff.staff_name + ' profile picture'"
                 (error)="onImageError($event)"
                 (load)="onImageLoad($event)"
                 loading="lazy"
                 referrerpolicy="no-referrer"
                 crossorigin="anonymous"
                 style="width: 100%; height: 100%; object-fit: cover; display: block;">
            
            <!-- Fallback Icon -->
            <div class="profile-fallback" *ngIf="!hasProfilePicture() || !getProfilePictureUrl()">
              <mat-icon class="fallback-icon">person</mat-icon>
              <span class="fallback-text">{{ staff.staff_name.charAt(0).toUpperCase() }}</span>
            </div>
          </div>

          <div class="profile-info">
            <h2>{{ staff.staff_name }}</h2>
            <p class="designation">{{ staff.designation_name }} - {{ staff.department_name }}</p>

            <div class="status-badges">
              <mat-chip-set>
                <mat-chip [class]="staff.is_active ? 'status-active' : 'status-inactive'">
                  <mat-icon>{{ staff.is_active ? 'check_circle' : 'cancel' }}</mat-icon>
                  {{ staff.is_active ? 'Active' : 'Inactive' }}
                </mat-chip>
                <mat-chip class="employment-type" *ngIf="staff.employment_type_name">
                  {{ staff.employment_type_name }}
                </mat-chip>
              </mat-chip-set>
            </div>

            <div class="quick-actions">
              <button mat-icon-button (click)="callMobile()" matTooltip="Call Mobile" color="primary">
                <mat-icon>phone</mat-icon>
              </button>
              <button mat-icon-button (click)="sendEmail()" matTooltip="Send Email" color="primary">
                <mat-icon>email</mat-icon>
              </button>
            </div>
          </div>

          <div class="profile-stats">
            <div class="stat-item">
              <span class="stat-label">Age</span>
              <span class="stat-value">{{ getAge() }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Experience</span>
              <span class="stat-value">{{ getExperience() }}</span>
            </div>
            <div class="stat-item" *ngIf="staff.salary_amount">
              <span class="stat-label">Salary</span>
              <span class="stat-value">{{ formatCurrency(staff.salary_amount) }}</span>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Detailed Information Tabs -->
    <mat-card class="detail-tabs">
      <mat-card-content>
        <mat-tab-group>
          <!-- Personal Information Tab -->
          <mat-tab label="Personal Information">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <label>Full Name</label>
                  <span>{{ staff.staff_name }}</span>
                </div>

                <div class="info-item">
                  <label>Email Address</label>
                  <span>
                    <a [href]="'mailto:' + staff.staff_email" class="link">{{ staff.staff_email }}</a>
                  </span>
                </div>

                <div class="info-item">
                  <label>Mobile Number</label>
                  <span>
                    <a [href]="'tel:' + staff.staff_mobile" class="link">{{ staff.staff_mobile }}</a>
                  </span>
                </div>

                <div class="info-item" *ngIf="staff.gender">
                  <label>Gender</label>
                  <span>{{ staff.gender }}</span>
                </div>

                <div class="info-item" *ngIf="staff.date_of_birth">
                  <label>Date of Birth</label>
                  <span>{{ formatDate(staff.date_of_birth) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.marital_status">
                  <label>Marital Status</label>
                  <span>{{ staff.marital_status }}</span>
                </div>

                <div class="info-item" *ngIf="staff.blood_group_name">
                  <label>Blood Group</label>
                  <span>{{ getDisplayValue(staff.blood_group_name) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.qualification_name">
                  <label>Qualification</label>
                  <span>{{ getDisplayValue(staff.qualification_name) }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Professional Information Tab -->
          <mat-tab label="Professional Details">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <label>Department</label>
                  <span>{{ staff.department_name }}</span>
                </div>

                <div class="info-item">
                  <label>Designation</label>
                  <span>{{ staff.designation_name }}</span>
                </div>

                <div class="info-item" *ngIf="staff.employment_type_name">
                  <label>Employment Type</label>
                  <span>{{ staff.employment_type_name }}</span>
                </div>

                <div class="info-item" *ngIf="staff.user_role_name">
                  <label>User Role</label>
                  <span>{{ getDisplayValue(staff.user_role_name) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.joining_date">
                  <label>Joining Date</label>
                  <span>{{ formatDate(staff.joining_date) }}</span>
                </div>

                <div class="info-item">
                  <label>Experience</label>
                  <span>{{ getExperience() }}</span>
                </div>

                <div class="info-item" *ngIf="staff.salary_amount">
                  <label>Current Salary</label>
                  <span>{{ formatCurrency(staff.salary_amount) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.salary_last_hiked_date">
                  <label>Last Salary Hike</label>
                  <span>{{ formatDate(staff.salary_last_hiked_date) }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Contact & Emergency Tab -->
          <mat-tab label="Contact & Emergency">
            <div class="tab-content">
              <h3>Contact Information</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>Email</label>
                  <span>
                    <a [href]="'mailto:' + staff.staff_email" class="link">{{ staff.staff_email }}</a>
                  </span>
                </div>

                <div class="info-item">
                  <label>Mobile</label>
                  <span>
                    <a [href]="'tel:' + staff.staff_mobile" class="link">{{ staff.staff_mobile }}</a>
                  </span>
                </div>
              </div>

              <mat-divider></mat-divider>

              <h3>Emergency Contact</h3>
              <div class="info-grid" *ngIf="staff.emergency_contact_name || staff.emergency_contact_phone">
                <div class="info-item" *ngIf="staff.emergency_contact_name">
                  <label>Contact Name</label>
                  <span>{{ staff.emergency_contact_name }}</span>
                </div>

                <div class="info-item" *ngIf="staff.emergency_contact_phone">
                  <label>Contact Phone</label>
                  <span>
                    <a [href]="'tel:' + staff.emergency_contact_phone" class="link">
                      {{ staff.emergency_contact_phone }}
                    </a>
                    <button mat-icon-button (click)="callEmergencyContact()" matTooltip="Call Emergency Contact" class="inline-action">
                      <mat-icon>phone</mat-icon>
                    </button>
                  </span>
                </div>

                <div class="info-item" *ngIf="staff.emergency_contact_relation">
                  <label>Relationship</label>
                  <span>{{ staff.emergency_contact_relation }}</span>
                </div>
              </div>
              <div *ngIf="!staff.emergency_contact_name && !staff.emergency_contact_phone" class="no-data-message">
                <mat-icon>info</mat-icon>
                <span>No emergency contact information available</span>
              </div>
            </div>
          </mat-tab>

          <!-- Address & Location Tab -->
          <mat-tab label="Address & Location">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item full-width" *ngIf="staff.staff_address">
                  <label>Complete Address</label>
                  <span>{{ staff.staff_address }}</span>
                </div>

                <div class="info-item" *ngIf="staff.locality_name">
                  <label>Locality</label>
                  <span>{{ getDisplayValue(staff.locality_name) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.city_name">
                  <label>City</label>
                  <span>{{ getDisplayValue(staff.city_name) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.state_name">
                  <label>State</label>
                  <span>{{ getDisplayValue(staff.state_name) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.pincode">
                  <label>Pincode</label>
                  <span>{{ staff.pincode }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Documents & Banking Tab -->
          <mat-tab label="Documents & Banking">
            <div class="tab-content">
              <h3>Identity Documents</h3>
              <div class="info-grid">
                <div class="info-item" *ngIf="staff.aadhaar_number">
                  <label>Aadhaar Number</label>
                  <span>{{ getMaskedValue(staff.aadhaar_number || '', 4) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.pan_number">
                  <label>PAN Number</label>
                  <span>{{ getMaskedValue(staff.pan_number || '', 4) }}</span>
                </div>
              </div>

              <mat-divider *ngIf="staff.bank_name || staff.account_number || staff.ifsc_code"></mat-divider>

              <h3 *ngIf="staff.bank_name || staff.account_number || staff.ifsc_code">Bank Details</h3>
              <div class="info-grid" *ngIf="staff.bank_name || staff.account_number || staff.ifsc_code">
                <div class="info-item" *ngIf="staff.bank_name">
                  <label>Bank Name</label>
                  <span>{{ getDisplayValue(staff.bank_name) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.account_number">
                  <label>Account Number</label>
                  <span>{{ getMaskedValue(staff.account_number || '', 4) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.ifsc_code">
                  <label>IFSC Code</label>
                  <span>{{ getDisplayValue(staff.ifsc_code) }}</span>
                </div>
              </div>

              <mat-divider *ngIf="staff.health_insurance_provider || staff.health_insurance_number"></mat-divider>

              <h3 *ngIf="staff.health_insurance_provider || staff.health_insurance_number">Health Insurance</h3>
              <div class="info-grid" *ngIf="staff.health_insurance_provider || staff.health_insurance_number">
                <div class="info-item" *ngIf="staff.health_insurance_provider">
                  <label>Insurance Provider</label>
                  <span>{{ getDisplayValue(staff.health_insurance_provider) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.health_insurance_number">
                  <label>Policy Number</label>
                  <span>{{ getMaskedValue(staff.health_insurance_number || '', 4) }}</span>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- System Information Tab -->
          <mat-tab label="System Info">
            <div class="tab-content">
              <div class="info-grid">
                <div class="info-item">
                  <label>Staff ID</label>
                  <span>{{ staff.id }}</span>
                </div>

                <div class="info-item">
                  <label>Status</label>
                  <span class="status-text" [class.active]="staff.is_active" [class.inactive]="!staff.is_active">
                    {{ staff.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </div>

                <div class="info-item" *ngIf="staff.created_by_username">
                  <label>Created By</label>
                  <span>{{ getDisplayValue(staff.created_by_username) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.created_at">
                  <label>Created Date</label>
                  <span>{{ formatDate(staff.created_at) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.updated_by_username">
                  <label>Last Updated By</label>
                  <span>{{ getDisplayValue(staff.updated_by_username) }}</span>
                </div>

                <div class="info-item" *ngIf="staff.updated_at">
                  <label>Last Updated</label>
                  <span>{{ formatDate(staff.updated_at) }}</span>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>
  </div>
</div>