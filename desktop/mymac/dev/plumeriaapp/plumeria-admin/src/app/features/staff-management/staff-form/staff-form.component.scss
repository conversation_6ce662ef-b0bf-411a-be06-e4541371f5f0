.staff-form-container {
  padding: 24px;
  max-width: 900px;
  margin: 0 auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  h1 {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }
}

.header-actions {
  display: flex;
  gap: 12px;

  button {
    min-width: 120px;
    height: 40px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }

    &[color="accent"] {
      background-color: #ff4081;
      color: white;
      border: 1px solid #ff4081;

      &:hover {
        background-color: #e91e63;
        border-color: #e91e63;
      }

      &:disabled {
        background-color: #f5f5f5;
        color: #bdbdbd;
        border-color: #e0e0e0;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

.form-card {
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

mat-card-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  mat-card-title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 16px;
  font-size: 16px;
}

// Form layout with 3 fields per row
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-field {
  flex: 1;
  min-width: 0; // Prevents flex items from overflowing

  &.full-width {
    flex: 1 1 100%;
  }

  &.half-width {
    flex: 1 1 calc(50% - 10px);
  }

  &.third-width {
    flex: 1 1 calc(33.333% - 14px);
  }
}

mat-form-field {
  width: 100%;

  .mat-mdc-form-field-label {
    font-weight: 500;
    color: #555;
  }

  &.mat-focused .mat-mdc-form-field-label {
    color: #3f51b5;
  }
}

// Special styling for specific field types
.photo-upload-field {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  text-align: center;

  &:hover {
    border-color: #3f51b5;
  }
}

.toggle-field {
  margin: 16px 0;

  mat-slide-toggle {
    .mat-mdc-slide-toggle-label {
      font-weight: 500;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;

  button {
    min-width: 120px;
    height: 44px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;

    &[mat-raised-button] {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

// Tab styling for organized sections
mat-tab-group {
  margin-bottom: 24px;

  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
  }

  .mat-mdc-tab-label {
    min-width: 120px;
    padding: 0 16px;
    font-weight: 500;

    &.mat-mdc-tab-label-active {
      color: #3f51b5;
    }
  }
}

.tab-content {
  padding: 24px 0;

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
    border-bottom: 2px solid #3f51b5;
    padding-bottom: 8px;
    display: inline-block;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .staff-form-container {
    padding: 16px;
  }

  .form-row {
    gap: 16px;
  }

  .form-field {
    &.third-width {
      flex: 1 1 calc(50% - 8px);
    }
  }
}

@media (max-width: 768px) {
  .staff-form-container {
    padding: 12px;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .form-field {
    &.third-width,
    &.half-width {
      flex: 1 1 100%;
    }
  }

  .form-actions {
    flex-direction: column-reverse;

    button {
      width: 100%;
    }
  }

  mat-tab-group {
    .mat-mdc-tab-label {
      min-width: 80px;
      padding: 0 8px;
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  mat-card-header {
    mat-card-title {
      font-size: 20px;
    }
  }

  .tab-content h3 {
    font-size: 16px;
  }
}

// Professional Searchable Dropdown Styling
.search-option {
  padding: 0 !important;
  height: auto !important;
  min-height: 56px !important;
  pointer-events: none !important;
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e0e0e0 !important;

  .search-container {
    width: 100%;
    padding: 12px 16px;
    pointer-events: all;

    .search-input {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      background-color: white;
      outline: none;
      transition: border-color 0.2s ease;

      &:focus {
        border-color: #3f51b5;
        box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.1);
      }

      &::placeholder {
        color: #999;
        font-style: italic;
      }
    }
  }
}

.divider-option {
  padding: 0 !important;
  height: 1px !important;
  min-height: 1px !important;

  .dropdown-divider {
    margin: 0;
    border: none;
    border-top: 1px solid #e0e0e0;
    width: 100%;
  }
}

.department-option,
.designation-option,
.qualification-option,
.employment-type-option {
  .option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .department-name,
    .designation-name,
    .qualification-name,
    .employment-type-name {
      font-weight: 500;
      color: #333;
    }
  }

  &:hover .option-content {
    .department-name,
    .designation-name,
    .qualification-name,
    .employment-type-name {
      color: #3f51b5;
    }
  }
}

.create-new-option {
  background-color: #f0f8ff !important;
  border-top: 1px solid #e3f2fd !important;
  border-bottom: 1px solid #e3f2fd !important;
  color: #1976d2 !important;
  font-weight: 500 !important;
  margin: 4px 0 !important;

  .create-option-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 4px 0;

    mat-icon {
      color: #4caf50;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    span {
      color: #1976d2;
      font-weight: 600;
      font-size: 14px;
    }
  }

  &:hover {
    background-color: #e3f2fd !important;

    .create-option-content {
      mat-icon {
        color: #388e3c;
      }

      span {
        color: #1565c0;
      }
    }
  }
}

.no-results-option, .empty-state-option {
  .no-results-content, .empty-state-content {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #666;
    font-style: italic;

    mat-icon {
      color: #999;
      font-size: 18px;
    }
  }
}

// Mobile App Access Section Styling
.mobile-access-section {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  margin: 16px 0;

  h3 {
    color: #333;
    margin-bottom: 16px;
    border-bottom: 2px solid #3f51b5;
    padding-bottom: 8px;
    display: inline-block;
  }

  h4 {
    color: #333;
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 500;
  }

  .mobile-status {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 16px;
  }
}

// Override Material Select Panel Styling
.mat-mdc-select-panel {
  max-height: 400px !important;

  .mat-mdc-option {
    &:hover:not(.mat-mdc-option-disabled) {
      background-color: rgba(63, 81, 181, 0.04) !important;
    }

    &.mat-mdc-option-active {
      background-color: rgba(63, 81, 181, 0.08) !important;
      color: #3f51b5 !important;
    }
  }
}